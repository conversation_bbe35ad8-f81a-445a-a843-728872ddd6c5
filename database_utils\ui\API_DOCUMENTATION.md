# 规则引擎管理系统 API 接口文档

## 概述

本文档描述了规则引擎管理系统的RESTful API接口，提供词表、条件、规则的完整CRUD操作。

**基础URL**: `http://localhost:8000`

**API版本**: v1.0.0

## 通用响应格式

### 成功响应
```json
{
    "success": true,
    "data": {...}  // 具体数据内容
}
```

### 错误响应
```json
{
    "success": false,
    "error": "错误描述信息"
}
```

## 1. 词表管理 API

### 1.1 获取所有词表
- **URL**: `/api/wordlists`
- **方法**: `GET`
- **描述**: 获取系统中所有词表的列表

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "黑名单词表",
            "description": "包含违规词汇的词表",
            "word_count": 25
        }
    ]
}
```

### 1.2 获取指定词表
- **URL**: `/api/wordlists/{wordlist_id}`
- **方法**: `GET`
- **描述**: 获取指定词表的详细信息

**路径参数**:
- `wordlist_id` (int): 词表ID

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "黑名单词表",
        "description": "包含违规词汇的词表",
        "word_count": 25,
        "created_at": "2024-01-01T10:00:00",
        "updated_at": "2024-01-01T10:00:00"
    }
}
```

### 1.3 创建词表
- **URL**: `/api/wordlists`
- **方法**: `POST`
- **描述**: 创建新的词表

**请求参数** (Form Data):
- `name` (string, 必需): 词表名称
- `description` (string, 可选): 词表描述

**响应示例**:
```json
{
    "success": true,
    "wordlist_id": 1
}
```

### 1.4 更新词表
- **URL**: `/api/wordlists/{wordlist_id}`
- **方法**: `PUT`
- **描述**: 更新词表信息

**路径参数**:
- `wordlist_id` (int): 词表ID

**请求参数** (Form Data):
- `name` (string, 可选): 新的词表名称
- `description` (string, 可选): 新的词表描述

**响应示例**:
```json
{
    "success": true
}
```

### 1.5 删除词表
- **URL**: `/api/wordlists/{wordlist_id}`
- **方法**: `DELETE`
- **描述**: 删除指定词表及其所有词汇

**路径参数**:
- `wordlist_id` (int): 词表ID

**响应示例**:
```json
{
    "success": true
}
```

### 1.6 获取词表词汇
- **URL**: `/wordlists/{wordlist_id}/words`
- **方法**: `GET`
- **描述**: 获取指定词表中的所有词汇

**路径参数**:
- `wordlist_id` (int): 词表ID

**响应示例**:
```json
{
    "words": ["违规词1", "违规词2", "违规词3"]
}
```

### 1.7 添加词汇到词表
- **URL**: `/api/wordlists/{wordlist_id}/words`
- **方法**: `POST`
- **描述**: 向指定词表添加词汇

**路径参数**:
- `wordlist_id` (int): 词表ID

**请求参数** (Form Data):
- `words` (string, 必需): 词汇列表，每行一个词汇

**响应示例**:
```json
{
    "success": true,
    "added_count": 3
}
```

### 1.8 从词表删除词汇
- **URL**: `/api/wordlists/{wordlist_id}/words`
- **方法**: `DELETE`
- **描述**: 从指定词表删除词汇

**路径参数**:
- `wordlist_id` (int): 词表ID

**请求参数** (Form Data):
- `words` (string, 必需): 要删除的词汇列表，每行一个词汇

**响应示例**:
```json
{
    "success": true,
    "removed_count": 2
}
```

## 2. 条件管理 API

### 2.1 获取所有条件
- **URL**: `/api/conditions`
- **方法**: `GET`
- **描述**: 获取系统中所有条件的列表

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "condition_name": "标题包含黑名单词汇",
            "field_name": "title",
            "operator": "in_wordlist",
            "value": null,
            "wordlist_id": 1,
            "wordlist_name": "黑名单词表"
        }
    ]
}
```

### 2.2 获取指定条件
- **URL**: `/api/conditions/{condition_id}`
- **方法**: `GET`
- **描述**: 获取指定条件的详细信息

**路径参数**:
- `condition_id` (int): 条件ID

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "condition_name": "标题包含黑名单词汇",
        "field_name": "title",
        "operator": "in_wordlist",
        "value": null,
        "wordlist_id": 1,
        "wordlist_name": "黑名单词表"
    }
}
```

### 2.3 创建条件
- **URL**: `/api/conditions`
- **方法**: `POST`
- **描述**: 创建新的条件

**请求参数** (Form Data):
- `condition_name` (string, 必需): 条件名称
- `field_name` (string, 必需): 字段名称 (title, subtitle, ocr_content, landing_url, advertiser_id, industry)
- `operator` (string, 必需): 操作符 (contains, not_contains, equals, not_equals, in_wordlist, not_in_wordlist)
- `value` (string, 可选): 限制值（当使用词表时为空）
- `wordlist_id` (int, 可选): 词表ID（当operator为词表相关操作时使用）

**响应示例**:
```json
{
    "success": true,
    "condition_id": 1
}
```

### 2.4 更新条件
- **URL**: `/api/conditions/{condition_id}`
- **方法**: `PUT`
- **描述**: 更新条件信息

**路径参数**:
- `condition_id` (int): 条件ID

**请求参数** (Form Data):
- `condition_name` (string, 可选): 新的条件名称
- `field_name` (string, 可选): 新的字段名称
- `operator` (string, 可选): 新的操作符
- `value` (string, 可选): 新的限制值
- `wordlist_id` (int, 可选): 新的词表ID

**响应示例**:
```json
{
    "success": true
}
```

### 2.5 删除条件
- **URL**: `/api/conditions/{condition_id}`
- **方法**: `DELETE`
- **描述**: 删除指定条件

**路径参数**:
- `condition_id` (int): 条件ID

**响应示例**:
```json
{
    "success": true
}
```

## 3. 规则管理 API

### 3.1 获取所有规则
- **URL**: `/api/rules`
- **方法**: `GET`
- **描述**: 获取系统中所有规则的列表（包括禁用的）

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "rule_name": "黑名单检查规则",
            "rule_description": "检查标题是否包含黑名单词汇",
            "logic_expression": "1",
            "status": "active",
            "priority": 10,
            "action": "reject"
        }
    ]
}
```

### 3.2 获取启用的规则
- **URL**: `/api/rules/active`
- **方法**: `GET`
- **描述**: 获取所有启用的规则

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "rule_name": "黑名单检查规则",
            "rule_description": "检查标题是否包含黑名单词汇",
            "logic_expression": "1",
            "status": "active",
            "priority": 10,
            "action": "reject"
        }
    ]
}
```

### 3.3 获取指定规则
- **URL**: `/api/rules/{rule_id}`
- **方法**: `GET`
- **描述**: 获取指定规则的详细信息

**路径参数**:
- `rule_id` (int): 规则ID

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "rule_name": "黑名单检查规则",
        "rule_description": "检查标题是否包含黑名单词汇",
        "logic_expression": "1",
        "status": "active",
        "priority": 10,
        "action": "reject"
    }
}
```

### 3.4 创建规则
- **URL**: `/api/rules`
- **方法**: `POST`
- **描述**: 创建新的规则

**请求参数** (Form Data):
- `rule_name` (string, 必需): 规则名称
- `logic_expression` (string, 必需): 逻辑表达式，如：(1 AND 2) OR (3 AND 4)
- `rule_description` (string, 可选): 规则描述
- `priority` (int, 可选): 规则优先级，默认为0
- `action` (string, 可选): 规则动作，默认为"reject" (reject/approve)

**响应示例**:
```json
{
    "success": true,
    "rule_id": 1
}
```

### 3.5 更新规则
- **URL**: `/api/rules/{rule_id}`
- **方法**: `PUT`
- **描述**: 更新规则信息

**路径参数**:
- `rule_id` (int): 规则ID

**请求参数** (Form Data):
- `rule_name` (string, 可选): 新的规则名称
- `logic_expression` (string, 可选): 新的逻辑表达式
- `rule_description` (string, 可选): 新的规则描述
- `priority` (int, 可选): 新的规则优先级
- `action` (string, 可选): 新的规则动作
- `status` (string, 可选): 新的规则状态 (active/inactive)

**响应示例**:
```json
{
    "success": true
}
```

### 3.6 更新规则状态
- **URL**: `/api/rules/{rule_id}/status`
- **方法**: `PUT`
- **描述**: 更新规则状态（启用/禁用）

**路径参数**:
- `rule_id` (int): 规则ID

**请求参数** (Form Data):
- `status` (string, 必需): 规则状态 (active/inactive)

**响应示例**:
```json
{
    "success": true
}
```

### 3.7 删除规则
- **URL**: `/api/rules/{rule_id}`
- **方法**: `DELETE`
- **描述**: 删除指定规则

**路径参数**:
- `rule_id` (int): 规则ID

**响应示例**:
```json
{
    "success": true
}
```

## 4. 统计信息 API

### 4.1 获取系统统计
- **URL**: `/api/stats`
- **方法**: `GET`
- **描述**: 获取系统统计信息

**响应示例**:
```json
{
    "wordlist_count": 5,
    "condition_count": 12,
    "active_rule_count": 8,
    "total_words": 150
}
```

## 5. 测试 API

### 5.1 测试创意审核
- **URL**: `/api/test-creative`
- **方法**: `POST`
- **描述**: 测试创意审核功能

**请求参数** (Form Data):
- `creative_id` (int, 必需): 创意ID
- `industry` (string, 必需): 行业
- `title` (string, 必需): 标题
- `subtitle` (string, 可选): 副标题
- `image_ocr` (string, 可选): 图片OCR内容
- `ldp_url` (string, 可选): 落地页URL
- `advertiser_id` (int, 可选): 广告主ID
- `request_id` (string, 可选): 请求ID

**响应示例**:
```json
{
    "success": true,
    "result": {
        "creative_id": 123,
        "audit_result": "reject",
        "hit_rules": [
            {
                "rule_id": 1,
                "rule_name": "黑名单检查规则",
                "action": "reject"
            }
        ],
        "audit_time": "2024-01-01T10:00:00"
    }
}
```

## 6. 错误代码说明

| 错误类型 | 描述 | 示例 |
|---------|------|------|
| 400 | 请求参数错误 | 缺少必需参数或参数格式错误 |
| 404 | 资源不存在 | 指定的词表/条件/规则不存在 |
| 409 | 资源冲突 | 名称已存在 |
| 500 | 服务器内部错误 | 数据库连接失败等 |

## 7. 字段类型说明

### 7.1 字段名称 (field_name)
- `title`: 标题
- `subtitle`: 副标题
- `ocr_content`: OCR内容
- `landing_url`: 落地页URL
- `advertiser_id`: 广告主ID
- `industry`: 行业

### 7.2 操作符 (operator)
- `contains`: 包含
- `not_contains`: 不包含
- `equals`: 等于
- `not_equals`: 不等于
- `in_wordlist`: 在词表中
- `not_in_wordlist`: 不在词表中

### 7.3 规则状态 (status)
- `active`: 启用
- `inactive`: 禁用

### 7.4 规则动作 (action)
- `reject`: 拒绝
- `approve`: 通过

## 8. 使用示例

### 8.1 创建完整的审核规则流程

1. **创建词表**:
```bash
curl -X POST http://localhost:8000/api/wordlists \
  -F "name=违规词表" \
  -F "description=包含违规内容的词汇"
```

2. **添加词汇**:
```bash
curl -X POST http://localhost:8000/api/wordlists/1/words \
  -F "words=违规词1
违规词2
违规词3"
```

3. **创建条件**:
```bash
curl -X POST http://localhost:8000/api/conditions \
  -F "condition_name=标题违规检查" \
  -F "field_name=title" \
  -F "operator=in_wordlist" \
  -F "wordlist_id=1"
```

4. **创建规则**:
```bash
curl -X POST http://localhost:8000/api/rules \
  -F "rule_name=违规内容拒绝规则" \
  -F "logic_expression=1" \
  -F "rule_description=检查标题是否包含违规词汇" \
  -F "priority=10" \
  -F "action=reject"
```

5. **测试审核**:
```bash
curl -X POST http://localhost:8000/api/test-creative \
  -F "creative_id=123" \
  -F "industry=电商" \
  -F "title=包含违规词1的标题" \
  -F "request_id=test_001"
```

## 9. 注意事项

1. **数据一致性**: 删除词表时会同时删除相关的条件，删除条件时需要检查是否被规则引用
2. **逻辑表达式**: 规则的逻辑表达式中的数字对应条件ID，支持AND、OR、括号等逻辑运算
3. **优先级**: 规则按优先级从高到低执行，数字越大优先级越高
4. **状态管理**: 只有启用状态的规则才会参与审核过程
5. **错误处理**: 所有API都包含详细的错误信息，便于调试和问题定位
