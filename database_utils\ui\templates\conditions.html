{% extends "base.html" %}

{% block title %}条件管理 - 规则引擎管理系统{% endblock %}

{% block extra_css %}
<style>
/* 自定义tooltip样式 */
.condition-tooltip-custom {
    max-width: 400px !important;
}

.condition-tooltip-custom .tooltip-inner {
    background-color: #2c3e50 !important;
    color: white !important;
    font-size: 13px !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
    white-space: nowrap !important;
}

.condition-tooltip-custom .tooltip-arrow::before {
    border-top-color: #2c3e50 !important;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-filter"></i>
        条件管理
    </h1>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createConditionModal">
        <i class="fas fa-plus"></i>
        创建条件
    </button>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="搜索条件名称...">
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="fieldFilter">
            <option value="">所有字段</option>
            <option value="title">标题</option>
            <option value="subtitle">副标题</option>
            <option value="ocr_content">OCR内容</option>
            <option value="landing_url">落地页URL</option>
            <option value="advertiser_id">广告主ID</option>
            <option value="industry">行业</option>
        </select>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="operatorFilter">
            <option value="">所有操作</option>
            <option value="contains">包含</option>
            <option value="not_contains">不包含</option>
            <option value="equals">等于</option>
            <option value="not_equals">不等于</option>
            <option value="in_wordlist">在词表中</option>
            <option value="not_in_wordlist">不在词表中</option>
        </select>
    </div>
</div>

<!-- 条件列表 -->
<div class="card">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-list"></i>
            条件列表 ({{ conditions|length }})
        </h6>
    </div>
    <div class="card-body">
        {% if conditions %}
        <div class="table-responsive">
            <table class="table table-hover" id="conditionsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>条件名称</th>
                        <th>字段</th>
                        <th>操作</th>
                        <th>值/词表</th>
                        <th>创建时间</th>
                        <th>说明</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for condition in conditions %}
                    <tr>
                        <td><span class="badge bg-secondary">{{ condition.id }}</span></td>
                        <td><strong>{{ condition.condition_name }}</strong></td>
                        <td>
                            <span class="badge bg-info">
                                {% if condition.field_name == 'title' %}标题
                                {% elif condition.field_name == 'subtitle' %}副标题
                                {% elif condition.field_name == 'ocr_content' %}OCR内容
                                {% elif condition.field_name == 'landing_url' %}落地页URL
                                {% elif condition.field_name == 'advertiser_id' %}广告主ID
                                {% elif condition.field_name == 'industry' %}行业
                                {% else %}{{ condition.field_name }}
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-warning">
                                {% if condition.operator == 'contains' %}包含
                                {% elif condition.operator == 'not_contains' %}不包含
                                {% elif condition.operator == 'equals' %}等于
                                {% elif condition.operator == 'not_equals' %}不等于
                                {% elif condition.operator == 'in_wordlist' %}在词表中
                                {% elif condition.operator == 'not_in_wordlist' %}不在词表中
                                {% else %}{{ condition.operator }}
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            {% if condition.wordlist_id %}
                                <span class="badge bg-success">
                                    <i class="fas fa-list"></i>
                                    {{ condition.wordlist_name or '词表' + condition.wordlist_id|string }}
                                </span>
                            {% elif condition.value %}
                                <code>{{ condition.value }}</code>
                            {% else %}
                                <span class="text-muted">无</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ condition.created_at or '未知' }}
                            </small>
                        </td>
                        <td>
                            <span class="btn btn-outline-info btn-sm condition-tooltip"
                                  data-bs-toggle="tooltip"
                                  data-bs-placement="top"
                                  data-bs-html="true"
                                  data-field-name="{% if condition.field_name == 'title' %}标题{% elif condition.field_name == 'subtitle' %}副标题{% elif condition.field_name == 'ocr_content' %}OCR内容{% elif condition.field_name == 'landing_url' %}落地页URL{% elif condition.field_name == 'advertiser_id' %}广告主ID{% elif condition.field_name == 'industry' %}行业{% else %}{{ condition.field_name }}{% endif %}"
                                  data-operator="{% if condition.operator == 'contains' %}包含{% elif condition.operator == 'not_contains' %}不包含{% elif condition.operator == 'equals' %}等于{% elif condition.operator == 'not_equals' %}不等于{% elif condition.operator == 'in_wordlist' %}在词表中{% elif condition.operator == 'not_in_wordlist' %}不在词表中{% else %}{{ condition.operator }}{% endif %}"
                                  data-value="{% if condition.wordlist_id %}{{ condition.wordlist_name or '词表' }}{% else %}{{ condition.value or '无' }}{% endif %}"
                                  data-value-type="{% if condition.wordlist_id %}wordlist{% else %}value{% endif %}"
                                  style="cursor: help;">
                                <i class="fas fa-info-circle"></i>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary"
                                        onclick="event.stopPropagation(); editCondition({{ condition.id }}, '{{ condition.condition_name }}', '{{ condition.field_name }}', '{{ condition.operator }}', '{{ condition.value or '' }}', {{ condition.wordlist_id or 'null' }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="event.stopPropagation(); showDeleteConfirm({{ condition.id }}, '{{ condition.condition_name }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center text-muted py-5">
            <i class="fas fa-inbox fa-4x mb-3"></i>
            <h4>暂无条件</h4>
            <p>条件需要通过数据库管理器创建</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 条件详情模态框 -->
<div class="modal fade" id="conditionDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">条件详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="conditionDetailContent">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>
</div>

<!-- 创建条件模态框 -->
<div class="modal fade" id="createConditionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建新条件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createConditionForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="conditionName" class="form-label">条件名称</label>
                        <input type="text" class="form-control" id="conditionName" name="condition_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="fieldName" class="form-label">字段名称</label>
                        <select class="form-select" id="fieldName" name="field_name" required>
                            <option value="">请选择字段</option>
                            <option value="title">标题</option>
                            <option value="subtitle">副标题</option>
                            <option value="ocr_content">OCR内容</option>
                            <option value="landing_url">落地页URL</option>
                            <option value="advertiser_id">广告主ID</option>
                            <option value="industry">行业</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="operator" class="form-label">操作符</label>
                        <select class="form-select" id="operator" name="operator" required onchange="toggleValueInput()">
                            <option value="">请选择操作符</option>
                            <option value="contains">包含</option>
                            <option value="not_contains">不包含</option>
                            <option value="equals">等于</option>
                            <option value="not_equals">不等于</option>
                            <option value="in_wordlist">在词表中</option>
                            <option value="not_in_wordlist">不在词表中</option>
                        </select>
                    </div>
                    <div class="mb-3" id="valueGroup">
                        <label for="value" class="form-label">限制值</label>
                        <input type="text" class="form-control" id="value" name="value">
                    </div>
                    <div class="mb-3" id="wordlistGroup" style="display: none;">
                        <label for="wordlistId" class="form-label">词表</label>
                        <select class="form-select" id="wordlistId" name="wordlist_id">
                            <option value="">请选择词表</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑条件模态框 -->
<div class="modal fade" id="editConditionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑条件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editConditionForm">
                <div class="modal-body">
                    <input type="hidden" id="editConditionId" name="condition_id">
                    <div class="mb-3">
                        <label for="editConditionName" class="form-label">条件名称</label>
                        <input type="text" class="form-control" id="editConditionName" name="condition_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editFieldName" class="form-label">字段名称</label>
                        <select class="form-select" id="editFieldName" name="field_name" required>
                            <option value="">请选择字段</option>
                            <option value="title">标题</option>
                            <option value="subtitle">副标题</option>
                            <option value="ocr_content">OCR内容</option>
                            <option value="landing_url">落地页URL</option>
                            <option value="advertiser_id">广告主ID</option>
                            <option value="industry">行业</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editOperator" class="form-label">操作符</label>
                        <select class="form-select" id="editOperator" name="operator" required onchange="toggleEditValueInput()">
                            <option value="">请选择操作符</option>
                            <option value="contains">包含</option>
                            <option value="not_contains">不包含</option>
                            <option value="equals">等于</option>
                            <option value="not_equals">不等于</option>
                            <option value="in_wordlist">在词表中</option>
                            <option value="not_in_wordlist">不在词表中</option>
                        </select>
                    </div>
                    <div class="mb-3" id="editValueGroup">
                        <label for="editValue" class="form-label">限制值</label>
                        <input type="text" class="form-control" id="editValue" name="value">
                    </div>
                    <div class="mb-3" id="editWordlistGroup" style="display: none;">
                        <label for="editWordlistId" class="form-label">词表</label>
                        <select class="form-select" id="editWordlistId" name="wordlist_id">
                            <option value="">请选择词表</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    确定要删除条件 "<span id="deleteConditionName"></span>" 吗？
                </div>
                <p class="text-muted">此操作不可恢复，请谨慎操作。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 初始化tooltip
document.addEventListener('DOMContentLoaded', function() {
    // 等待一下确保DOM完全加载
    setTimeout(function() {
        var tooltipElements = document.querySelectorAll('.condition-tooltip');
        console.log('找到tooltip元素:', tooltipElements.length);

        tooltipElements.forEach(function(element) {
            var fieldName = element.getAttribute('data-field-name');
            var operator = element.getAttribute('data-operator');
            var value = element.getAttribute('data-value');
            var valueType = element.getAttribute('data-value-type');

            // 根据值类型设置不同的颜色
            var valueColor = valueType === 'wordlist' ? '#28a745' : '#007bff'; // 词表用绿色，值用蓝色

            var tooltipText = `当广告的 <span style="color: #dc3545; font-weight: bold;">${fieldName}</span> 字段 <span style="color: #6f42c1; font-weight: bold;">${operator}</span> <span style="color: ${valueColor}; font-weight: bold;">${value}</span> 时，此条件为真`;

            new bootstrap.Tooltip(element, {
                trigger: 'hover focus',
                placement: 'top',
                html: true,
                title: tooltipText,
                customClass: 'condition-tooltip-custom'
            });
        });
    }, 100);
});

// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    filterTable();
});

document.getElementById('fieldFilter').addEventListener('change', function() {
    filterTable();
});

document.getElementById('operatorFilter').addEventListener('change', function() {
    filterTable();
});

function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const fieldFilter = document.getElementById('fieldFilter').value;
    const operatorFilter = document.getElementById('operatorFilter').value;

    const table = document.getElementById('conditionsTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const conditionName = row.cells[1].textContent.toLowerCase();
        const fieldName = row.cells[2].textContent;
        const operator = row.cells[3].textContent;

        let showRow = true;

        // 搜索过滤
        if (searchTerm && !conditionName.includes(searchTerm)) {
            showRow = false;
        }

        // 字段过滤
        if (fieldFilter && !fieldName.includes(fieldFilter)) {
            showRow = false;
        }

        // 操作过滤
        if (operatorFilter && !operator.includes(operatorFilter)) {
            showRow = false;
        }

        row.style.display = showRow ? '' : 'none';
    }
}

// 移除行点击事件，因为所有信息都在表格中显示

function showConditionDetail(row) {
    const cells = row.cells;
    const conditionId = cells[0].textContent.trim();
    const conditionName = cells[1].textContent.trim();
    const fieldName = cells[2].textContent.trim();
    const operator = cells[3].textContent.trim();
    const value = cells[4].textContent.trim();
    const createdAt = cells[5].textContent.trim();

    const detailHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>ID:</strong></td><td>${conditionId}</td></tr>
                    <tr><td><strong>名称:</strong></td><td>${conditionName}</td></tr>
                    <tr><td><strong>创建时间:</strong></td><td>${createdAt}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>条件配置</h6>
                <table class="table table-sm">
                    <tr><td><strong>字段:</strong></td><td>${fieldName}</td></tr>
                    <tr><td><strong>操作:</strong></td><td>${operator}</td></tr>
                    <tr><td><strong>值/词表:</strong></td><td>${value}</td></tr>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <h6>条件说明</h6>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    当广告的 <strong>${fieldName}</strong> 字段 <strong>${operator}</strong> <code>${value}</code> 时，此条件为真。
                </div>
            </div>
        </div>
    `;

    document.getElementById('conditionDetailContent').innerHTML = detailHtml;
    $('#conditionDetailModal').modal('show');
}

// 加载词表选项
function loadWordlists() {
    fetch('/api/wordlists')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const wordlistSelect = document.getElementById('wordlistId');
            const editWordlistSelect = document.getElementById('editWordlistId');

            // 清空现有选项
            wordlistSelect.innerHTML = '<option value="">请选择词表</option>';
            editWordlistSelect.innerHTML = '<option value="">请选择词表</option>';

            // 添加词表选项
            data.data.forEach(wordlist => {
                const option = `<option value="${wordlist.id}">${wordlist.name}</option>`;
                wordlistSelect.innerHTML += option;
                editWordlistSelect.innerHTML += option;
            });
        }
    })
    .catch(error => console.error('加载词表失败:', error));
}

// 页面加载时加载词表
document.addEventListener('DOMContentLoaded', function() {
    loadWordlists();
});

// 切换值输入框显示
function toggleValueInput() {
    const operator = document.getElementById('operator').value;
    const valueGroup = document.getElementById('valueGroup');
    const wordlistGroup = document.getElementById('wordlistGroup');

    if (operator === 'in_wordlist' || operator === 'not_in_wordlist') {
        valueGroup.style.display = 'none';
        wordlistGroup.style.display = 'block';
        document.getElementById('value').required = false;
        document.getElementById('wordlistId').required = true;
    } else {
        valueGroup.style.display = 'block';
        wordlistGroup.style.display = 'none';
        document.getElementById('value').required = true;
        document.getElementById('wordlistId').required = false;
    }
}

function toggleEditValueInput() {
    const operator = document.getElementById('editOperator').value;
    const valueGroup = document.getElementById('editValueGroup');
    const wordlistGroup = document.getElementById('editWordlistGroup');

    if (operator === 'in_wordlist' || operator === 'not_in_wordlist') {
        valueGroup.style.display = 'none';
        wordlistGroup.style.display = 'block';
        document.getElementById('editValue').required = false;
        document.getElementById('editWordlistId').required = true;
    } else {
        valueGroup.style.display = 'block';
        wordlistGroup.style.display = 'none';
        document.getElementById('editValue').required = true;
        document.getElementById('editWordlistId').required = false;
    }
}

// 创建条件
$('#createConditionForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('/api/conditions', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#createConditionModal').modal('hide');
            location.reload();
        } else {
            alert('创建失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('创建失败: ' + error);
    });
});

// 编辑条件
function editCondition(id, name, fieldName, operator, value, wordlistId) {
    document.getElementById('editConditionId').value = id;
    document.getElementById('editConditionName').value = name;
    document.getElementById('editFieldName').value = fieldName;
    document.getElementById('editOperator').value = operator;
    document.getElementById('editValue').value = value;
    document.getElementById('editWordlistId').value = wordlistId || '';

    toggleEditValueInput();
    $('#editConditionModal').modal('show');
}

$('#editConditionForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const conditionId = formData.get('condition_id');

    fetch(`/api/conditions/${conditionId}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#editConditionModal').modal('hide');
            location.reload();
        } else {
            alert('更新失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('更新失败: ' + error);
    });
});

// 显示删除确认框
function showDeleteConfirm(conditionId, conditionName) {
    document.getElementById('deleteConditionName').textContent = conditionName;
    document.getElementById('confirmDeleteBtn').onclick = function() {
        deleteCondition(conditionId);
    };
    $('#deleteConfirmModal').modal('show');
}

// 删除条件
function deleteCondition(conditionId) {
    fetch(`/api/conditions/${conditionId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        $('#deleteConfirmModal').modal('hide');
        if (data.success) {
            showToast('条件删除成功', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('删除失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        $('#deleteConfirmModal').modal('hide');
        showToast('删除失败: ' + error, 'error');
    });
}
</script>
{% endblock %}
