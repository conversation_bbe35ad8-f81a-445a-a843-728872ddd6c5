{% extends "base.html" %}

{% block title %}规则管理 - 规则引擎管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-sitemap"></i>
        规则管理
    </h1>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createRuleModal">
        <i class="fas fa-plus"></i>
        创建规则
    </button>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="搜索规则名称或描述...">
        </div>
    </div>
    <div class="col-md-4">
        <select class="form-select" id="priorityFilter">
            <option value="">所有优先级</option>
            <option value="high">高优先级 (≥8)</option>
            <option value="medium">中优先级 (4-7)</option>
            <option value="low">低优先级 (≤3)</option>
        </select>
    </div>
</div>

<!-- 规则列表 -->
<div class="row">
    {% for rule in rules %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold">{{ rule.rule_name }}</h6>
                <div class="d-flex align-items-center">
                    <span class="badge bg-{% if rule.priority >= 8 %}danger{% elif rule.priority >= 4 %}warning{% else %}secondary{% endif %} me-2">
                        优先级 {{ rule.priority }}
                    </span>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="viewRuleDetail({{ rule.id }})">
                                <i class="fas fa-eye"></i> 查看详情
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="toggleRuleStatus({{ rule.id }}, '{{ rule.status }}')">
                                <i class="fas fa-{% if rule.status == 'active' %}pause{% else %}play{% endif %}"></i>
                                {% if rule.status == 'active' %}禁用{% else %}启用{% endif %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="editRule({{ rule.id }}, '{{ rule.rule_name }}', '{{ rule.logic_expression }}', '{{ rule.rule_description or '' }}', {{ rule.priority }}, '{{ rule.action }}', '{{ rule.status }}')">
                                <i class="fas fa-edit"></i> 编辑规则
                            </a></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="showDeleteRuleConfirm({{ rule.id }}, '{{ rule.rule_name }}')">
                                <i class="fas fa-trash"></i> 删除规则
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p class="card-text">{{ rule.rule_description or '无描述' }}</p>

                <div class="mb-3">
                    <small class="text-muted">逻辑表达式:</small>
                    <div class="mt-1">
                        <code class="logic-expression">{{ rule.logic_expression }}</code>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <small class="text-muted">动作</small>
                    <span class="badge bg-{% if rule.action == 'approve' %}success{% else %}danger{% endif %}">
                        <i class="fas fa-{% if rule.action == 'approve' %}check{% else %}times{% endif %}"></i>
                        {% if rule.action == 'approve' %}通过{% else %}拒绝{% endif %}
                    </span>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">状态</small>
                    <span class="badge bg-{% if rule.status == 'active' %}success{% else %}secondary{% endif %}">
                        <i class="fas fa-{% if rule.status == 'active' %}check{% else %}pause{% endif %}"></i>
                        {% if rule.status == 'active' %}启用{% else %}禁用{% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    {% if not rules %}
    <div class="col-12">
        <div class="text-center text-muted py-5">
            <i class="fas fa-inbox fa-4x mb-3"></i>
            <h4>暂无规则</h4>
            <p>规则需要通过数据库管理器创建</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- 规则详情模态框 -->
<div class="modal fade" id="ruleDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">规则详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="ruleDetailContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.logic-expression {
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    word-break: break-all;
}

.condition-badge {
    margin: 0.125rem;
    font-size: 0.75rem;
}

.rule-flow {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
}
</style>

<!-- 创建规则模态框 -->
<div class="modal fade" id="createRuleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建新规则</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createRuleForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="ruleName" class="form-label">规则名称</label>
                        <input type="text" class="form-control" id="ruleName" name="rule_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="ruleDescription" class="form-label">规则描述</label>
                        <textarea class="form-control" id="ruleDescription" name="rule_description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="logicExpression" class="form-label">逻辑表达式</label>
                        <input type="text" class="form-control" id="logicExpression" name="logic_expression" required
                               placeholder="例如: (1 AND 2) OR 3">
                        <div class="form-text">使用条件ID和逻辑运算符(AND, OR)构建表达式，支持括号</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="priority" class="form-label">优先级</label>
                            <input type="number" class="form-control" id="priority" name="priority" value="0" min="0" max="100">
                            <div class="form-text">数字越大优先级越高</div>
                        </div>
                        <div class="col-md-6">
                            <label for="action" class="form-label">规则动作</label>
                            <select class="form-select" id="action" name="action" required>
                                <option value="reject">拒绝</option>
                                <option value="approve">通过</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑规则模态框 -->
<div class="modal fade" id="editRuleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑规则</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editRuleForm">
                <div class="modal-body">
                    <input type="hidden" id="editRuleId" name="rule_id">
                    <div class="mb-3">
                        <label for="editRuleName" class="form-label">规则名称</label>
                        <input type="text" class="form-control" id="editRuleName" name="rule_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editRuleDescription" class="form-label">规则描述</label>
                        <textarea class="form-control" id="editRuleDescription" name="rule_description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editLogicExpression" class="form-label">逻辑表达式</label>
                        <input type="text" class="form-control" id="editLogicExpression" name="logic_expression" required
                               placeholder="例如: (1 AND 2) OR 3">
                        <div class="form-text">使用条件ID和逻辑运算符(AND, OR)构建表达式，支持括号</div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <label for="editPriority" class="form-label">优先级</label>
                            <input type="number" class="form-control" id="editPriority" name="priority" value="0" min="0" max="100">
                            <div class="form-text">数字越大优先级越高</div>
                        </div>
                        <div class="col-md-4">
                            <label for="editAction" class="form-label">规则动作</label>
                            <select class="form-select" id="editAction" name="action" required>
                                <option value="reject">拒绝</option>
                                <option value="approve">通过</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="editStatus" class="form-label">规则状态</label>
                            <select class="form-select" id="editStatus" name="status" required>
                                <option value="active">启用</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    filterRules();
});

document.getElementById('priorityFilter').addEventListener('change', function() {
    filterRules();
});

function filterRules() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const priorityFilter = document.getElementById('priorityFilter').value;

    const cards = document.querySelectorAll('.col-lg-6');

    cards.forEach(card => {
        const ruleName = card.querySelector('.card-header h6').textContent.toLowerCase();
        const ruleDesc = card.querySelector('.card-text').textContent.toLowerCase();
        const priorityBadge = card.querySelector('.badge');
        const priority = parseInt(priorityBadge.textContent.match(/\d+/)[0]);

        let showCard = true;

        // 搜索过滤
        if (searchTerm && !ruleName.includes(searchTerm) && !ruleDesc.includes(searchTerm)) {
            showCard = false;
        }

        // 优先级过滤
        if (priorityFilter) {
            if (priorityFilter === 'high' && priority < 8) showCard = false;
            if (priorityFilter === 'medium' && (priority < 4 || priority > 7)) showCard = false;
            if (priorityFilter === 'low' && priority > 3) showCard = false;
        }

        card.style.display = showCard ? '' : 'none';
    });
}

// 查看规则详情
function viewRuleDetail(ruleId) {
    $('#ruleDetailModal').modal('show');

    // 这里可以通过API获取更详细的规则信息
    // 暂时显示基本信息
    const ruleCard = document.querySelector(`[onclick="viewRuleDetail(${ruleId})"]`).closest('.card');
    const ruleName = ruleCard.querySelector('.card-header h6').textContent;
    const ruleDesc = ruleCard.querySelector('.card-text').textContent;
    const logicExpr = ruleCard.querySelector('.logic-expression').textContent;
    const priority = ruleCard.querySelector('.badge').textContent;
    const badges = ruleCard.querySelectorAll('.card-body .badge');
    const action = badges[0].textContent;
    const status = badges[1].textContent;

    const detailHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>ID:</strong></td><td>${ruleId}</td></tr>
                    <tr><td><strong>名称:</strong></td><td>${ruleName}</td></tr>
                    <tr><td><strong>描述:</strong></td><td>${ruleDesc}</td></tr>
                    <tr><td><strong>优先级:</strong></td><td>${priority}</td></tr>
                    <tr><td><strong>动作:</strong></td><td>${action}</td></tr>
                    <tr><td><strong>状态:</strong></td><td>${status}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>逻辑表达式</h6>
                <div class="rule-flow">
                    <code>${logicExpr}</code>
                </div>
                <small class="text-muted mt-2 d-block">
                    <i class="fas fa-info-circle"></i>
                    数字代表条件ID，AND/OR表示逻辑关系
                </small>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <h6>规则说明</h6>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    此规则会根据逻辑表达式 <code>${logicExpr}</code> 来判断广告是否符合条件。
                    当表达式结果为真时，广告将被此规则命中。
                </div>
            </div>
        </div>
    `;

    document.getElementById('ruleDetailContent').innerHTML = detailHtml;
}

// 切换规则状态
function toggleRuleStatus(ruleId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? '启用' : '禁用';

    if (confirm(`确定要${action}这个规则吗？`)) {
        const formData = new FormData();
        formData.append('status', newStatus);

        fetch(`/api/rules/${ruleId}/status`, {
            method: 'PUT',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(`${action}失败: ` + data.error);
            }
        })
        .catch(error => {
            alert(`${action}失败: ` + error);
        });
    }
}

// 创建规则
$('#createRuleForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('/api/rules', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#createRuleModal').modal('hide');
            location.reload();
        } else {
            alert('创建失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('创建失败: ' + error);
    });
});

// 编辑规则
function editRule(id, name, logicExpression, description, priority, action, status) {
    document.getElementById('editRuleId').value = id;
    document.getElementById('editRuleName').value = name;
    document.getElementById('editLogicExpression').value = logicExpression;
    document.getElementById('editRuleDescription').value = description;
    document.getElementById('editPriority').value = priority;
    document.getElementById('editAction').value = action;
    document.getElementById('editStatus').value = status;

    $('#editRuleModal').modal('show');
}

$('#editRuleForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const ruleId = formData.get('rule_id');

    fetch(`/api/rules/${ruleId}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#editRuleModal').modal('hide');
            location.reload();
        } else {
            alert('更新失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('更新失败: ' + error);
    });
});

// 删除规则
function deleteRule(ruleId) {
    fetch(`/api/rules/${ruleId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        $('#deleteRuleConfirmModal').modal('hide');
        if (data.success) {
            location.reload();
        } else {
            alert('删除失败: ' + data.error);
        }
    })
    .catch(error => {
        $('#deleteRuleConfirmModal').modal('hide');
        alert('删除失败: ' + error);
    });
}

// 显示删除确认框
function showDeleteRuleConfirm(ruleId, ruleName) {
    document.getElementById('deleteRuleName').textContent = ruleName;
    document.getElementById('confirmDeleteRuleBtn').onclick = function() {
        deleteRule(ruleId);
    };
    $('#deleteRuleConfirmModal').modal('show');
}
</script>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteRuleConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    确定要删除规则 "<span id="deleteRuleName"></span>" 吗？
                </div>
                <p class="text-muted">此操作不可恢复，请谨慎操作。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteRuleBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
