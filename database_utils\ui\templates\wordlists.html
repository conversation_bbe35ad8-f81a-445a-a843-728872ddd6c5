{% extends "base.html" %}

{% block title %}词表管理 - 规则引擎管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-list"></i>
        词表管理
    </h1>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createWordlistModal">
        <i class="fas fa-plus"></i>
        创建词表
    </button>
</div>

<!-- 词表列表 -->
<div class="row">
    {% for wordlist in wordlists %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold">{{ wordlist.name }}</h6>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="viewWords({{ wordlist.id }})">
                            <i class="fas fa-eye"></i> 查看词汇
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="addWords({{ wordlist.id }})">
                            <i class="fas fa-plus"></i> 添加词汇
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="removeWords({{ wordlist.id }})">
                            <i class="fas fa-minus"></i> 删除词汇
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="editWordlist({{ wordlist.id }}, '{{ wordlist.name }}', '{{ wordlist.description or '' }}')">
                            <i class="fas fa-edit"></i> 编辑词表
                        </a></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteWordlist({{ wordlist.id }})">
                            <i class="fas fa-trash"></i> 删除词表
                        </a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <p class="card-text">{{ wordlist.description or '无描述' }}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">词汇数量</small>
                    <span class="badge bg-primary">{{ wordlist.word_count }}</span>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    {% if not wordlists %}
    <div class="col-12">
        <div class="text-center text-muted py-5">
            <i class="fas fa-inbox fa-4x mb-3"></i>
            <h4>暂无词表</h4>
            <p>点击上方按钮创建第一个词表</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- 创建词表模态框 -->
<div class="modal fade" id="createWordlistModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建新词表</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createWordlistForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="wordlistName" class="form-label">词表名称</label>
                        <input type="text" class="form-control" id="wordlistName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="wordlistDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="wordlistDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 查看词汇模态框 -->
<div class="modal fade" id="viewWordsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">词汇列表</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="wordsContainer">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑词表模态框 -->
<div class="modal fade" id="editWordlistModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑词表</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editWordlistForm">
                <div class="modal-body">
                    <input type="hidden" id="editWordlistId" name="wordlist_id">
                    <div class="mb-3">
                        <label for="editWordlistName" class="form-label">词表名称</label>
                        <input type="text" class="form-control" id="editWordlistName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editWordlistDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="editWordlistDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 添加词汇模态框 -->
<div class="modal fade" id="addWordsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加词汇</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addWordsForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="wordsText" class="form-label">词汇列表（每行一个）</label>
                        <textarea class="form-control" id="wordsText" name="words" rows="10"
                                  placeholder="请输入词汇，每行一个"></textarea>
                    </div>
                    <input type="hidden" id="targetWordlistId" name="wordlist_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除词汇模态框 -->
<div class="modal fade" id="removeWordsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">删除词汇</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="removeWordsForm">
                <div class="modal-body">
                    <input type="hidden" id="removeWordlistId" name="wordlist_id">
                    <div class="mb-3">
                        <label for="removeWordsInput" class="form-label">要删除的词汇</label>
                        <textarea class="form-control" id="removeWordsInput" name="words" rows="10"
                                  placeholder="每行输入一个要删除的词汇" required></textarea>
                        <div class="form-text">每行输入一个要删除的词汇</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">删除</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 创建词表
$('#createWordlistForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('/api/wordlists', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('创建失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('创建失败: ' + error);
    });
});

// 查看词汇
function viewWords(wordlistId) {
    $('#viewWordsModal').modal('show');

    fetch(`/wordlists/${wordlistId}/words`)
    .then(response => response.json())
    .then(data => {
        const container = document.getElementById('wordsContainer');
        if (data.words && data.words.length > 0) {
            const wordsHtml = data.words.map(word =>
                `<span class="badge bg-secondary me-1 mb-1">${word}</span>`
            ).join('');
            container.innerHTML = `<div>${wordsHtml}</div>`;
        } else {
            container.innerHTML = '<div class="text-center text-muted">暂无词汇</div>';
        }
    })
    .catch(error => {
        document.getElementById('wordsContainer').innerHTML =
            '<div class="text-center text-danger">加载失败</div>';
    });
}

// 添加词汇
function addWords(wordlistId) {
    document.getElementById('targetWordlistId').value = wordlistId;
    $('#addWordsModal').modal('show');
}

$('#addWordsForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const wordlistId = formData.get('wordlist_id');

    fetch(`/api/wordlists/${wordlistId}/words`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#addWordsModal').modal('hide');
            alert(`成功添加 ${data.added_count} 个词汇`);
            location.reload();
        } else {
            alert('添加失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('添加失败: ' + error);
    });
});

// 编辑词表
function editWordlist(wordlistId, name, description) {
    document.getElementById('editWordlistId').value = wordlistId;
    document.getElementById('editWordlistName').value = name;
    document.getElementById('editWordlistDescription').value = description;
    $('#editWordlistModal').modal('show');
}

$('#editWordlistForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const wordlistId = formData.get('wordlist_id');

    fetch(`/api/wordlists/${wordlistId}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#editWordlistModal').modal('hide');
            location.reload();
        } else {
            alert('更新失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('更新失败: ' + error);
    });
});

// 删除词汇
function removeWords(wordlistId) {
    document.getElementById('removeWordlistId').value = wordlistId;
    $('#removeWordsModal').modal('show');
}

$('#removeWordsForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const wordlistId = formData.get('wordlist_id');

    fetch(`/api/wordlists/${wordlistId}/words`, {
        method: 'DELETE',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#removeWordsModal').modal('hide');
            alert(`成功删除 ${data.removed_count} 个词汇`);
            location.reload();
        } else {
            alert('删除失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('删除失败: ' + error);
    });
});

// 删除词表
function deleteWordlist(wordlistId) {
    if (confirm('确定要删除这个词表吗？此操作不可恢复。')) {
        fetch(`/api/wordlists/${wordlistId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败: ' + data.error);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error);
        });
    }
}
</script>
{% endblock %}
