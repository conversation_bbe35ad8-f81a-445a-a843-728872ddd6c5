#!/usr/bin/env python3
"""
下载静态文件脚本
下载Bootstrap、j<PERSON><PERSON>y、Font Awesome等静态资源到本地
"""

import os
import requests
from pathlib import Path

# 静态文件目录
STATIC_DIR = Path("database_utils/ui/static")
STATIC_DIR.mkdir(exist_ok=True)

# 要下载的文件列表
FILES_TO_DOWNLOAD = [
    {
        "url": "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css",
        "filename": "bootstrap.min.css"
    },
    {
        "url": "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js",
        "filename": "bootstrap.bundle.min.js"
    },
    {
        "url": "https://code.jquery.com/jquery-3.6.0.min.js",
        "filename": "jquery.min.js"
    },
    {
        "url": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css",
        "filename": "fontawesome.min.css"
    }
]

def download_file(url, filename):
    """下载文件"""
    file_path = STATIC_DIR / filename
    
    # 如果文件已存在，跳过下载
    if file_path.exists():
        print(f"✅ {filename} 已存在，跳过下载")
        return True
    
    try:
        print(f"📥 正在下载 {filename}...")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        with open(file_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ {filename} 下载完成 ({len(response.content)} bytes)")
        return True
        
    except Exception as e:
        print(f"❌ {filename} 下载失败: {e}")
        return False

def main():
    print("🚀 开始下载静态文件...")
    
    success_count = 0
    total_count = len(FILES_TO_DOWNLOAD)
    
    for file_info in FILES_TO_DOWNLOAD:
        if download_file(file_info["url"], file_info["filename"]):
            success_count += 1
    
    print(f"\n📊 下载完成: {success_count}/{total_count} 个文件成功")
    
    if success_count == total_count:
        print("🎉 所有静态文件下载完成！")
        print("现在可以重启服务器以使用本地静态文件。")
    else:
        print("⚠️ 部分文件下载失败，请检查网络连接。")

if __name__ == "__main__":
    main()
