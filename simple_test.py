#!/usr/bin/env python3
"""
简单的性能测试
测试基本的HTTP请求性能
"""

import requests
import time

def test_simple_request():
    """测试简单请求"""
    url = "http://127.0.0.1:8000/api/stats"

    print("🔍 测试单个API请求...")

    # 预热请求
    try:
        requests.get(url, timeout=5)
        print("✅ 预热请求完成")
    except Exception as e:
        print(f"❌ 预热请求失败: {e}")
        return

    # 测试多次请求
    times = []
    for i in range(5):
        start_time = time.time()
        try:
            response = requests.get(url, timeout=5)
            end_time = time.time()

            if response.status_code == 200:
                request_time = (end_time - start_time) * 1000
                times.append(request_time)
                print(f"  请求 {i+1}: {request_time:.2f}ms")
            else:
                print(f"  请求 {i+1}: HTTP {response.status_code}")

        except Exception as e:
            print(f"  请求 {i+1}: 错误 - {e}")

    if times:
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)

        print(f"\n📊 统计结果:")
        print(f"   平均响应时间: {avg_time:.2f}ms")
        print(f"   最快响应时间: {min_time:.2f}ms")
        print(f"   最慢响应时间: {max_time:.2f}ms")

        if avg_time < 100:
            print("   🟢 性能: 优秀")
        elif avg_time < 500:
            print("   🟡 性能: 良好")
        elif avg_time < 1000:
            print("   🟠 性能: 一般")
        else:
            print("   🔴 性能: 需要优化")

def test_localhost_connection():
    """测试本地连接"""
    print("\n🌐 测试本地连接...")

    # 测试基本连接
    try:
        start_time = time.time()
        response = requests.get("http://127.0.0.1:8000", timeout=5)
        end_time = time.time()

        connection_time = (end_time - start_time) * 1000
        print(f"   连接时间: {connection_time:.2f}ms")
        print(f"   状态码: {response.status_code}")

    except Exception as e:
        print(f"   连接失败: {e}")

if __name__ == "__main__":
    print("🚀 开始简单性能测试...")

    test_localhost_connection()
    test_simple_request()

    print("\n🎉 测试完成！")
