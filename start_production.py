#!/usr/bin/env python3
"""
生产模式启动脚本
使用优化的配置启动服务器
"""

import uvicorn
import os
from pathlib import Path

# 设置环境变量
os.environ["ENVIRONMENT"] = "production"

if __name__ == "__main__":
    # 生产模式配置
    uvicorn.run(
        "database_utils.ui.main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # 关闭自动重载
        workers=1,     # 单进程模式
        access_log=False,  # 关闭访问日志
        log_level="warning"  # 减少日志输出
    )
