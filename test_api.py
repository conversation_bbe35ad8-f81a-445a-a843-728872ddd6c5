#!/usr/bin/env python3
"""
API接口测试脚本
测试规则引擎管理系统的各个API接口
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:8000"

def test_api():
    print("🚀 开始测试规则引擎管理系统API接口...")

    # 1. 测试获取统计信息
    print("\n📊 测试获取统计信息...")
    response = requests.get(f"{BASE_URL}/api/stats")
    if response.status_code == 200:
        stats = response.json()
        print(f"✅ 统计信息获取成功: {stats}")
    else:
        print(f"❌ 统计信息获取失败: {response.status_code}")

    # 2. 测试创建词表
    print("\n📝 测试创建词表...")
    timestamp = int(time.time())
    wordlist_data = {
        'name': f'API测试词表_{timestamp}',
        'description': '通过API创建的测试词表'
    }
    response = requests.post(f"{BASE_URL}/api/wordlists", data=wordlist_data)
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            wordlist_id = result['wordlist_id']
            print(f"✅ 词表创建成功，ID: {wordlist_id}")
        else:
            print(f"❌ 词表创建失败: {result['error']}")
            return
    else:
        print(f"❌ 词表创建请求失败: {response.status_code}")
        return

    # 3. 测试添加词汇到词表
    print("\n➕ 测试添加词汇...")
    words_data = {
        'words': '测试词汇1\n测试词汇2\n测试词汇3'
    }
    response = requests.post(f"{BASE_URL}/api/wordlists/{wordlist_id}/words", data=words_data)
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print(f"✅ 词汇添加成功，添加了 {result['added_count']} 个词汇")
        else:
            print(f"❌ 词汇添加失败: {result['error']}")
    else:
        print(f"❌ 词汇添加请求失败: {response.status_code}")

    # 4. 测试获取所有词表
    print("\n📋 测试获取所有词表...")
    response = requests.get(f"{BASE_URL}/api/wordlists")
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print(f"✅ 词表列表获取成功，共 {len(result['data'])} 个词表")
            for wordlist in result['data']:
                print(f"   - {wordlist['name']}: {wordlist['word_count']} 个词汇")
        else:
            print(f"❌ 词表列表获取失败: {result['error']}")
    else:
        print(f"❌ 词表列表请求失败: {response.status_code}")

    # 5. 测试创建条件
    print("\n🔍 测试创建条件...")
    condition_data = {
        'condition_name': f'API测试条件_{timestamp}',
        'field_name': 'title',
        'operator': 'in_wordlist',
        'wordlist_id': wordlist_id
    }
    response = requests.post(f"{BASE_URL}/api/conditions", data=condition_data)
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            condition_id = result['condition_id']
            print(f"✅ 条件创建成功，ID: {condition_id}")
        else:
            print(f"❌ 条件创建失败: {result['error']}")
            return
    else:
        print(f"❌ 条件创建请求失败: {response.status_code}")
        return

    # 6. 测试获取所有条件
    print("\n📋 测试获取所有条件...")
    response = requests.get(f"{BASE_URL}/api/conditions")
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print(f"✅ 条件列表获取成功，共 {len(result['data'])} 个条件")
            for condition in result['data']:
                print(f"   - {condition['condition_name']}: {condition['field_name']} {condition['operator']}")
        else:
            print(f"❌ 条件列表获取失败: {result['error']}")
    else:
        print(f"❌ 条件列表请求失败: {response.status_code}")

    # 7. 测试创建规则
    print("\n⚙️ 测试创建规则...")
    rule_data = {
        'rule_name': f'API测试规则_{timestamp}',
        'rule_description': '通过API创建的测试规则',
        'logic_expression': str(condition_id),
        'priority': 5,
        'action': 'reject'
    }
    response = requests.post(f"{BASE_URL}/api/rules", data=rule_data)
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            rule_id = result['rule_id']
            print(f"✅ 规则创建成功，ID: {rule_id}")
        else:
            print(f"❌ 规则创建失败: {result['error']}")
            return
    else:
        print(f"❌ 规则创建请求失败: {response.status_code}")
        return

    # 8. 测试获取所有规则
    print("\n📋 测试获取所有规则...")
    response = requests.get(f"{BASE_URL}/api/rules")
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print(f"✅ 规则列表获取成功，共 {len(result['data'])} 个规则")
            for rule in result['data']:
                print(f"   - {rule['rule_name']}: 优先级 {rule['priority']}, 状态 {rule['status']}")
        else:
            print(f"❌ 规则列表获取失败: {result['error']}")
    else:
        print(f"❌ 规则列表请求失败: {response.status_code}")

    # 9. 测试创意审核
    print("\n🧪 测试创意审核...")
    creative_data = {
        'creative_id': 12345,
        'industry': '电商',
        'title': '包含测试词汇1的标题',
        'subtitle': '副标题内容',
        'image_ocr': '',
        'ldp_url': 'https://example.com',
        'advertiser_id': 1001,
        'request_id': 'api_test_001'
    }
    response = requests.post(f"{BASE_URL}/api/test-creative", data=creative_data)
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            audit_result = result['result']
            print(f"✅ 创意审核成功")
            print(f"   - 审核结果: {audit_result['final_decision']}")
            print(f"   - 是否命中规则: {audit_result['is_hit']}")
            print(f"   - 命中规则数: {len(audit_result.get('hit_rules', []))}")
            if audit_result.get('hit_rules'):
                for rule in audit_result['hit_rules']:
                    print(f"     * {rule['rule_name']} (优先级: {rule['priority']}, 动作: {rule['action']})")
        else:
            print(f"❌ 创意审核失败: {result['error']}")
    else:
        print(f"❌ 创意审核请求失败: {response.status_code}")

    print("\n🎉 API接口测试完成！")

if __name__ == "__main__":
    try:
        test_api()
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务已启动 (python database_utils/ui/main.py)")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
