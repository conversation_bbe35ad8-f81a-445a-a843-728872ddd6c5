#!/usr/bin/env python3
"""
性能测试脚本
测试网页加载速度和响应时间
"""

import requests
import time
from urllib.parse import urljoin

BASE_URL = "http://127.0.0.1:8000"

def test_page_load_time(url, page_name):
    """测试页面加载时间"""
    try:
        start_time = time.time()
        response = requests.get(url, timeout=10)
        end_time = time.time()

        load_time = (end_time - start_time) * 1000  # 转换为毫秒

        if response.status_code == 200:
            print(f"✅ {page_name}: {load_time:.2f}ms")
            return load_time
        else:
            print(f"❌ {page_name}: HTTP {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ {page_name}: 错误 - {e}")
        return None

def test_static_resource_load_time(url, resource_name):
    """测试静态资源加载时间"""
    try:
        start_time = time.time()
        response = requests.get(url, timeout=10)
        end_time = time.time()

        load_time = (end_time - start_time) * 1000  # 转换为毫秒

        if response.status_code == 200:
            size_kb = len(response.content) / 1024
            print(f"✅ {resource_name}: {load_time:.2f}ms ({size_kb:.1f}KB)")
            return load_time
        else:
            print(f"❌ {resource_name}: HTTP {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ {resource_name}: 错误 - {e}")
        return None

def main():
    print("🚀 开始性能测试...")

    # 测试页面加载时间
    print("\n📄 测试页面加载时间:")
    pages = [
        ("/", "首页"),
        ("/wordlists", "词表管理"),
        ("/conditions", "条件管理"),
        ("/rules", "规则管理"),
        ("/test", "测试页面")
    ]

    page_times = []
    for path, name in pages:
        url = urljoin(BASE_URL, path)
        load_time = test_page_load_time(url, name)
        if load_time is not None:
            page_times.append(load_time)

    # 测试静态资源加载时间
    print("\n📦 测试静态资源加载时间:")
    static_resources = [
        ("/static/bootstrap.min.css", "Bootstrap CSS"),
        ("/static/bootstrap.bundle.min.js", "Bootstrap JS"),
        ("/static/jquery.min.js", "jQuery"),
        ("/static/custom.css", "自定义CSS")
    ]

    static_times = []
    for path, name in static_resources:
        url = urljoin(BASE_URL, path)
        load_time = test_static_resource_load_time(url, name)
        if load_time is not None:
            static_times.append(load_time)

    # 测试API响应时间
    print("\n🔌 测试API响应时间:")
    api_endpoints = [
        ("/api/stats", "统计信息"),
        ("/api/wordlists", "词表列表"),
        ("/api/conditions", "条件列表"),
        ("/api/rules", "规则列表")
    ]

    api_times = []
    for path, name in api_endpoints:
        url = urljoin(BASE_URL, path)
        load_time = test_page_load_time(url, name)
        if load_time is not None:
            api_times.append(load_time)

    # 统计结果
    print("\n📊 性能统计:")
    if page_times:
        avg_page_time = sum(page_times) / len(page_times)
        print(f"   页面平均加载时间: {avg_page_time:.2f}ms")
        print(f"   页面最快加载时间: {min(page_times):.2f}ms")
        print(f"   页面最慢加载时间: {max(page_times):.2f}ms")

    if static_times:
        avg_static_time = sum(static_times) / len(static_times)
        print(f"   静态资源平均加载时间: {avg_static_time:.2f}ms")
        print(f"   静态资源最快加载时间: {min(static_times):.2f}ms")
        print(f"   静态资源最慢加载时间: {max(static_times):.2f}ms")

    if api_times:
        avg_api_time = sum(api_times) / len(api_times)
        print(f"   API平均响应时间: {avg_api_time:.2f}ms")
        print(f"   API最快响应时间: {min(api_times):.2f}ms")
        print(f"   API最慢响应时间: {max(api_times):.2f}ms")

    # 性能评估
    print("\n🎯 性能评估:")
    if page_times:
        avg_page_time = sum(page_times) / len(page_times)
        if avg_page_time < 100:
            print("   🟢 页面加载速度: 优秀 (<100ms)")
        elif avg_page_time < 300:
            print("   🟡 页面加载速度: 良好 (100-300ms)")
        elif avg_page_time < 1000:
            print("   🟠 页面加载速度: 一般 (300-1000ms)")
        else:
            print("   🔴 页面加载速度: 需要优化 (>1000ms)")

    if static_times:
        avg_static_time = sum(static_times) / len(static_times)
        if avg_static_time < 50:
            print("   🟢 静态资源加载: 优秀 (<50ms)")
        elif avg_static_time < 200:
            print("   🟡 静态资源加载: 良好 (50-200ms)")
        elif avg_static_time < 500:
            print("   🟠 静态资源加载: 一般 (200-500ms)")
        else:
            print("   🔴 静态资源加载: 需要优化 (>500ms)")

    print("\n🎉 性能测试完成！")

if __name__ == "__main__":
    try:
        main()
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务已启动")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
